"""
数据库连接和初始化模块
"""
import os
import pymysql
import sqlalchemy
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import create_engine, text

# 初始化SQLAlchemy对象
db = SQLAlchemy()

def init_app(app):
    """
    初始化数据库连接

    Args:
        app: Flask应用实例
    """
    # 将SQLAlchemy附加到Flask应用
    db.init_app(app)

# 用于跟踪数据库是否已经初始化
_database_initialized = False

def init_database(app):
    """
    初始化数据库，加载database.sql文件

    Args:
        app: Flask应用实例
    """
    global _database_initialized

    # 如果数据库已经初始化，则跳过
    if _database_initialized:
        return

    try:
        # 获取数据库连接信息
        mysql_user = app.config['MYSQL_USER']
        mysql_password = app.config['MYSQL_PASSWORD']
        mysql_host = app.config['MYSQL_HOST']
        mysql_port = app.config['MYSQL_PORT']
        db_name = app.config['MYSQL_DB']

        # 创建数据库引擎（不指定具体的数据库）
        engine = create_engine(f'mysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}')

        # 连接并执行SQL命令
        with engine.connect() as connection:
            print(f"正在检查数据库 {db_name} 是否存在...")

            # 检查数据库是否存在
            result = connection.execute(text(f"SHOW DATABASES LIKE '{db_name}'"))
            db_exists = result.fetchone() is not None

            if not db_exists:
                print(f"数据库 {db_name} 不存在，正在创建并初始化...")

                # 创建数据库
                connection.execute(text(f"CREATE DATABASE IF NOT EXISTS {db_name}"))
                connection.execute(text(f"USE {db_name}"))

                # 直接从项目根目录读取SQL文件
                # 获取项目根目录路径
                root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                sql_file_path = os.path.join(root_dir, "database.sql")

                if os.path.exists(sql_file_path):
                    print(f"找到数据库文件: {sql_file_path}")
                    with open(sql_file_path, 'r', encoding='utf-8') as f:
                        sql_script = f.read()

                    # 分割SQL脚本，逐条执行
                    commands = sql_script.split(';')
                    for command in commands:
                        if command.strip():
                            try:
                                connection.execute(text(command))
                            except Exception as cmd_error:
                                print(f"执行SQL命令出错: {cmd_error}\n命令: {command}")

                    print("数据库初始化完成！")
                else:
                    print(f"错误：找不到数据库文件！已查找路径: {sql_file_path}")
            else:
                print(f"数据库 {db_name} 已存在，跳过初始化步骤。")

        # 标记数据库已初始化
        _database_initialized = True

    except Exception as e:
        print(f"数据库初始化出错: {e}")
