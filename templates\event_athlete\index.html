{% extends "layout.html" %}

{% block title %}比赛项目学生管理 - 运动会管理系统{% endblock %}

{% block page_title %}比赛项目学生管理{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0 text-primary">
                    <i class="bi bi-list-ul me-2"></i>比赛项目列表
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    {% for event in events %}
                    <a href="{{ url_for('event_athlete.index', event_id=event.event_id) }}" 
                       class="list-group-item list-group-item-action {% if selected_event and selected_event.event_id == event.event_id %}active{% endif %}">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ event.event_name }}</h6>
                            <small>{{ event.event_type }}</small>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <small>
                                {% if event.gender_limit != '不限' %}
                                <span class="badge bg-info">{{ event.gender_limit }}子项目</span>
                                {% endif %}
                                <span class="badge bg-secondary">{{ event.start_time.strftime('%Y-%m-%d %H:%M') if event.start_time else '时间未定' }}</span>
                            </small>
                            <small>{{ event.location or '地点未定' }}</small>
                        </div>
                    </a>
                    {% else %}
                    <div class="list-group-item text-center py-4">
                        <i class="bi bi-exclamation-circle text-muted mb-2" style="font-size: 2rem;"></i>
                        <p class="mb-0">暂无比赛项目</p>
                        {% if session.role == 'admin' %}
                        <a href="{{ url_for('event.add') }}" class="btn btn-sm btn-primary mt-2">
                            <i class="bi bi-plus-circle"></i> 添加比赛项目
                        </a>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="card shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
                <h5 class="mb-0 text-primary">
                    {% if selected_event %}
                    <i class="bi bi-people me-2"></i>{{ selected_event.event_name }} - 参赛学生列表
                    {% else %}
                    <i class="bi bi-people me-2"></i>参赛学生列表
                    {% endif %}
                </h5>
                {% if selected_event and session.role == 'admin' %}
                <div>
                    <button type="button" class="btn btn-outline-primary btn-sm me-1" data-bs-toggle="modal" data-bs-target="#importModal">
                        <i class="bi bi-upload"></i> 导入成绩
                    </button>
                    <a href="{{ url_for('event_athlete.batch_add', event_id=selected_event.event_id) }}" class="btn btn-outline-success btn-sm me-1">
                        <i class="bi bi-people"></i> 批量添加学生
                    </a>
                    <a href="{{ url_for('event_athlete.add', event_id=selected_event.event_id) }}" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle"></i> 添加学生
                    </a>
                </div>
                {% endif %}
            </div>
            
            {% if selected_event %}
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">项目信息</h5>
                            <p class="mb-0">
                                <strong>项目类型:</strong> {{ selected_event.event_type }} | 
                                <strong>性别限制:</strong> {{ selected_event.gender_limit }} | 
                                <strong>最大参与人数:</strong> {{ selected_event.max_participants or '不限' }} | 
                                <strong>开始时间:</strong> {{ selected_event.start_time.strftime('%Y-%m-%d %H:%M') if selected_event.start_time else '未设置' }} | 
                                <strong>比赛地点:</strong> {{ selected_event.location or '未设置' }}
                            </p>
                        </div>
                    </div>
                </div>
                
                {% if scores %}
                <div class="table-responsive">
                    <table class="table table-hover align-middle" id="scores-table">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>姓名</th>
                                <th>学号</th>
                                <th>性别</th>
                                <th>院系</th>
                                <th>成绩</th>
                                <th>排名</th>
                                <th>得分</th>
                                <th class="text-end">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for score in scores %}
                            <tr>
                                <td>{{ score.athlete.athlete_id }}</td>
                                <td>{{ score.athlete.name }}</td>
                                <td>{{ score.athlete.student_id }}</td>
                                <td>{{ score.athlete.gender }}</td>
                                <td>{{ score.athlete.department.dept_name }}</td>
                                <td>
                                    {% if session.role == 'admin' %}
                                    <input type="text" class="form-control form-control-sm result-input" 
                                           data-score-id="{{ score.score_id }}" 
                                           value="{{ score.result or '' }}" 
                                           placeholder="未录入">
                                    {% else %}
                                    {{ score.result or '未录入' }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if session.role == 'admin' %}
                                    <input type="number" class="form-control form-control-sm ranking-input" 
                                           data-score-id="{{ score.score_id }}" 
                                           value="{{ score.ranking or '' }}" 
                                           placeholder="未录入" min="1">
                                    {% else %}
                                    {{ score.ranking or '未录入' }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if session.role == 'admin' %}
                                    <input type="number" class="form-control form-control-sm points-input" 
                                           data-score-id="{{ score.score_id }}" 
                                           value="{{ score.points or '' }}" 
                                           placeholder="未录入" min="0">
                                    {% else %}
                                    {{ score.points or '未录入' }}
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    {% if session.role == 'admin' %}
                                    <a href="{{ url_for('event_athlete.edit', score_id=score.score_id) }}" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-btn" 
                                            data-score-id="{{ score.score_id }}" 
                                            data-athlete-name="{{ score.athlete.name }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% else %}
                                    <a href="{{ url_for('athlete.detail', athlete_id=score.athlete.athlete_id) }}" class="btn btn-sm btn-outline-info">
                                        <i class="bi bi-info-circle"></i> 查看
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if session.role == 'admin' %}
                <div class="text-end mt-3">
                    <button type="button" id="save-all-btn" class="btn btn-success">
                        <i class="bi bi-save"></i> 保存所有更改
                    </button>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                    <p class="mt-3 text-muted">该比赛项目暂无参赛学生</p>
                    {% if session.role == 'admin' %}
                    <div>
                        <a href="{{ url_for('event_athlete.batch_add', event_id=selected_event.event_id) }}" class="btn btn-outline-success btn-sm me-2">
                            <i class="bi bi-people"></i> 批量添加学生
                        </a>
                        <a href="{{ url_for('event_athlete.add', event_id=selected_event.event_id) }}" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle"></i> 添加学生
                        </a>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            {% else %}
            <div class="card-body text-center py-5">
                <i class="bi bi-calendar-event text-muted" style="font-size: 3rem;"></i>
                <p class="mt-3 text-muted">请选择一个比赛项目</p>
                {% if events|length == 0 and session.role == 'admin' %}
                <a href="{{ url_for('event.add') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus-circle"></i> 添加比赛项目
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 导入成绩模态框 -->
{% if selected_event and session.role == 'admin' %}
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">导入成绩</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="import-form" enctype="multipart/form-data">
                    <input type="hidden" name="event_id" value="{{ selected_event.event_id }}">
                    <div class="mb-3">
                        <label for="import-file" class="form-label">选择CSV文件</label>
                        <input type="file" class="form-control" id="import-file" name="file" accept=".csv" required>
                        <div class="form-text">
                            CSV文件应包含以下列：学号、成绩、排名、得分
                        </div>
                    </div>
                    <div class="mb-3">
                        <a href="#" id="download-template" class="text-decoration-none">
                            <i class="bi bi-download"></i> 下载模板
                        </a>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="import-btn">导入</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除 <span id="delete-athlete-name" class="fw-bold"></span> 的参赛记录吗？</p>
                <p class="text-danger"><i class="bi bi-exclamation-triangle-fill"></i> 此操作不可撤销！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="delete-form" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化DataTable
        $('#scores-table').DataTable({
            language: {
                url: "{{ url_for('static', filename='js/dataTables.chinese.json') }}"
            },
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]]
        });
        
        {% if session.role == 'admin' and selected_event %}
        // 保存所有更改
        $('#save-all-btn').on('click', function() {
            const scores = [];
            
            // 收集所有已更改的成绩
            $('.result-input, .ranking-input, .points-input').each(function() {
                const scoreId = $(this).data('score-id');
                const field = $(this).hasClass('result-input') ? 'result' : 
                              $(this).hasClass('ranking-input') ? 'ranking' : 'points';
                const value = $(this).val();
                
                // 查找是否已有该成绩的记录
                let scoreData = scores.find(s => s.score_id === scoreId);
                if (!scoreData) {
                    scoreData = { score_id: scoreId };
                    scores.push(scoreData);
                }
                
                // 设置字段值
                scoreData[field] = value === '' ? null : value;
            });
            
            // 发送请求
            $.ajax({
                url: "{{ url_for('event_athlete.batch_update') }}",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    event_id: {{ selected_event.event_id }},
                    scores: scores
                }),
                success: function(response) {
                    if (response.success) {
                        // 显示成功提示
                        const toast = $(`
                            <div class="toast align-items-center text-white bg-success border-0 position-fixed bottom-0 end-0 m-3" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="d-flex">
                                    <div class="toast-body">
                                        <i class="bi bi-check-circle me-2"></i>${response.message}
                                    </div>
                                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                            </div>
                        `);
                        
                        $('body').append(toast);
                        const toastInstance = new bootstrap.Toast(toast[0], { delay: 3000 });
                        toastInstance.show();
                    } else {
                        alert('保存失败: ' + response.message);
                    }
                },
                error: function(xhr) {
                    alert('保存失败: ' + xhr.responseText);
                }
            });
        });
        
        // 删除按钮点击事件
        $('.delete-btn').on('click', function() {
            const scoreId = $(this).data('score-id');
            const athleteName = $(this).data('athlete-name');
            
            // 设置模态框内容
            $('#delete-athlete-name').text(athleteName);
            $('#delete-form').attr('action', "{{ url_for('event_athlete.delete', score_id=0) }}".replace('0', scoreId));
            
            // 显示模态框
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });
        
        // 导入按钮点击事件
        $('#import-btn').on('click', function() {
            const formData = new FormData(document.getElementById('import-form'));
            
            // 检查是否选择了文件
            if (!$('#import-file').val()) {
                alert('请选择CSV文件');
                return;
            }
            
            // 发送请求
            $.ajax({
                url: "{{ url_for('event_athlete.import_scores') }}",
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // 关闭模态框
                        const importModal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
                        importModal.hide();
                        
                        // 显示成功提示
                        alert(response.message);
                        
                        // 刷新页面
                        location.reload();
                    } else {
                        alert('导入失败: ' + response.message);
                    }
                },
                error: function(xhr) {
                    alert('导入失败: ' + xhr.responseText);
                }
            });
        });
        
        // 下载模板
        $('#download-template').on('click', function(e) {
            e.preventDefault();
            
            // 创建CSV内容
            const csvContent = "学号,成绩,排名,得分\n";
            
            // 创建Blob对象
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = '成绩导入模板.csv';
            link.style.display = 'none';
            
            // 添加到文档并触发点击
            document.body.appendChild(link);
            link.click();
            
            // 清理
            document.body.removeChild(link);
        });
        {% endif %}
    });
</script>
{% endblock %}
