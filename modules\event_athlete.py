"""
比赛项目学生管理模块
用于管理比赛项目中的学生参与情况和成绩
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, IntegerField, HiddenField, SubmitField, FloatField
from wtforms.validators import DataRequired, Length, Optional, NumberRange
from modules.database import db
from modules.models import Event, Athlete, Score, Department
from modules.auth import login_required, admin_required
from sqlalchemy.exc import IntegrityError
import csv
import io

# 创建比赛项目学生管理蓝图
event_athlete_bp = Blueprint('event_athlete', __name__, url_prefix='/event_athlete')

# 学生成绩表单类
class ScoreForm(FlaskForm):
    """学生成绩表单类，用于添加和编辑学生成绩"""
    event_id = SelectField('比赛项目', coerce=int, validators=[DataRequired()])
    athlete_id = SelectField('运动员', coerce=int, validators=[DataRequired()])
    result = StringField('成绩', validators=[Optional(), Length(max=20)])
    ranking = IntegerField('排名', validators=[Optional(), NumberRange(min=1)])
    points = IntegerField('得分', validators=[Optional(), NumberRange(min=0)])
    submit = SubmitField('提交')

# 批量添加成绩表单类
class BatchScoreForm(FlaskForm):
    """批量添加成绩表单类"""
    event_id = SelectField('比赛项目', coerce=int, validators=[DataRequired()])
    department_id = SelectField('院系筛选', coerce=int, validators=[Optional()])
    submit = SubmitField('提交选中学生')

@event_athlete_bp.route('/')
@login_required
def index():
    """
    比赛项目学生列表页面
    
    Returns:
        比赛项目学生列表页面
    """
    # 获取所有比赛项目
    events = Event.query.order_by(Event.event_id.desc()).all()
    
    # 获取选中的项目ID
    event_id = request.args.get('event_id', type=int)
    
    # 如果没有选中项目且有项目存在，默认选择第一个
    if not event_id and events:
        event_id = events[0].event_id
    
    # 获取选中项目的信息
    selected_event = None
    scores = []
    
    if event_id:
        selected_event = Event.query.get_or_404(event_id)
        scores = Score.query.filter_by(event_id=event_id).all()
    
    return render_template('event_athlete/index.html', 
                          events=events, 
                          selected_event=selected_event, 
                          scores=scores)

@event_athlete_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add():
    """
    添加学生到比赛项目
    
    Returns:
        添加学生到比赛项目页面或重定向到比赛项目学生列表页面
    """
    form = ScoreForm()
    
    # 获取所有比赛项目
    events = Event.query.all()
    form.event_id.choices = [(e.event_id, f"{e.event_name} ({e.event_type})") for e in events]
    
    # 获取所有运动员
    athletes = Athlete.query.all()
    form.athlete_id.choices = [(a.athlete_id, f"{a.name} ({a.student_id} - {a.department.dept_name})") for a in athletes]
    
    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        event_id = form.event_id.data
        athlete_id = form.athlete_id.data
        result = form.result.data
        ranking = form.ranking.data
        points = form.points.data
        
        # 检查运动员是否已经参加了该比赛项目
        existing_score = Score.query.filter_by(event_id=event_id, athlete_id=athlete_id).first()
        if existing_score:
            flash('该运动员已经参加了此比赛项目！', 'danger')
            return render_template('event_athlete/add.html', form=form)
        
        # 创建成绩记录
        score = Score(
            event_id=event_id,
            athlete_id=athlete_id,
            result=result,
            ranking=ranking,
            points=points
        )
        
        try:
            # 保存到数据库
            db.session.add(score)
            db.session.commit()
            
            flash('学生成功添加到比赛项目！', 'success')
            return redirect(url_for('event_athlete.index', event_id=event_id))
        except IntegrityError:
            db.session.rollback()
            flash('添加失败，该运动员已经参加了此比赛项目！', 'danger')
    
    # 从URL参数获取预选的比赛项目
    preselected_event_id = request.args.get('event_id', type=int)
    if preselected_event_id:
        form.event_id.data = preselected_event_id
    
    return render_template('event_athlete/add.html', form=form)

@event_athlete_bp.route('/batch_add', methods=['GET', 'POST'])
@login_required
@admin_required
def batch_add():
    """
    批量添加学生到比赛项目
    
    Returns:
        批量添加学生到比赛项目页面或重定向到比赛项目学生列表页面
    """
    form = BatchScoreForm()
    
    # 获取所有比赛项目
    events = Event.query.all()
    form.event_id.choices = [(e.event_id, f"{e.event_name} ({e.event_type})") for e in events]
    
    # 获取所有院系
    departments = Department.query.all()
    form.department_id.choices = [(0, '所有院系')] + [(d.dept_id, d.dept_name) for d in departments]
    
    # 从URL参数获取预选的比赛项目
    preselected_event_id = request.args.get('event_id', type=int)
    if preselected_event_id:
        form.event_id.data = preselected_event_id
    
    # 处理表单提交
    if request.method == 'POST' and 'submit' in request.form:
        event_id = form.event_id.data
        selected_athletes = request.form.getlist('selected_athletes')
        
        if not selected_athletes:
            flash('请至少选择一名运动员！', 'warning')
            return redirect(url_for('event_athlete.batch_add', event_id=event_id))
        
        # 获取已经参加该项目的运动员
        existing_athletes = db.session.query(Score.athlete_id).filter(Score.event_id == event_id).all()
        existing_athlete_ids = [a[0] for a in existing_athletes]
        
        # 添加成绩记录
        added_count = 0
        for athlete_id in selected_athletes:
            athlete_id = int(athlete_id)
            
            # 检查运动员是否已经参加了该比赛项目
            if athlete_id in existing_athlete_ids:
                continue
            
            # 创建成绩记录
            score = Score(
                event_id=event_id,
                athlete_id=athlete_id,
                result=None,
                ranking=None,
                points=None
            )
            
            db.session.add(score)
            added_count += 1
        
        try:
            db.session.commit()
            flash(f'成功添加 {added_count} 名运动员到比赛项目！', 'success')
            return redirect(url_for('event_athlete.index', event_id=event_id))
        except IntegrityError:
            db.session.rollback()
            flash('添加失败，可能有运动员已经参加了此比赛项目！', 'danger')
    
    # 获取符合条件的运动员
    event_id = form.event_id.data if form.event_id.data else preselected_event_id
    department_id = form.department_id.data
    
    athletes = []
    if event_id:
        # 获取选中项目
        event = Event.query.get(event_id)
        
        # 构建查询
        query = Athlete.query
        
        # 应用性别限制
        if event and event.gender_limit != '不限':
            query = query.filter(Athlete.gender == event.gender_limit)
        
        # 应用院系筛选
        if department_id and department_id > 0:
            query = query.filter(Athlete.dept_id == department_id)
        
        # 获取已经参加该项目的运动员
        existing_athletes = db.session.query(Score.athlete_id).filter(Score.event_id == event_id).all()
        existing_athlete_ids = [a[0] for a in existing_athletes]
        
        # 获取未参加该项目的运动员
        athletes = query.all()
        athletes = [a for a in athletes if a.athlete_id not in existing_athlete_ids]
    
    return render_template('event_athlete/batch_add.html', 
                          form=form, 
                          athletes=athletes, 
                          selected_event_id=event_id)

@event_athlete_bp.route('/edit/<int:score_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit(score_id):
    """
    编辑学生成绩
    
    Args:
        score_id: 成绩ID
        
    Returns:
        编辑学生成绩页面或重定向到比赛项目学生列表页面
    """
    # 查询成绩记录
    score = Score.query.get_or_404(score_id)
    
    # 创建表单
    form = ScoreForm(obj=score)
    
    # 获取所有比赛项目
    events = Event.query.all()
    form.event_id.choices = [(e.event_id, f"{e.event_name} ({e.event_type})") for e in events]
    
    # 获取所有运动员
    athletes = Athlete.query.all()
    form.athlete_id.choices = [(a.athlete_id, f"{a.name} ({a.student_id} - {a.department.dept_name})") for a in athletes]
    
    # 设置默认值
    form.event_id.data = score.event_id
    form.athlete_id.data = score.athlete_id
    
    # 处理表单提交
    if form.validate_on_submit():
        # 获取表单数据
        result = form.result.data
        ranking = form.ranking.data
        points = form.points.data
        
        # 更新成绩记录
        score.result = result
        score.ranking = ranking
        score.points = points
        
        # 保存到数据库
        db.session.commit()
        
        flash('成绩更新成功！', 'success')
        return redirect(url_for('event_athlete.index', event_id=score.event_id))
    
    return render_template('event_athlete/edit.html', form=form, score=score)

@event_athlete_bp.route('/delete/<int:score_id>', methods=['POST'])
@login_required
@admin_required
def delete(score_id):
    """
    删除学生成绩
    
    Args:
        score_id: 成绩ID
        
    Returns:
        重定向到比赛项目学生列表页面
    """
    # 查询成绩记录
    score = Score.query.get_or_404(score_id)
    event_id = score.event_id
    
    # 删除成绩记录
    db.session.delete(score)
    db.session.commit()
    
    flash('成绩记录已删除！', 'success')
    return redirect(url_for('event_athlete.index', event_id=event_id))

@event_athlete_bp.route('/batch_update', methods=['POST'])
@login_required
@admin_required
def batch_update():
    """
    批量更新成绩
    
    Returns:
        JSON响应
    """
    data = request.json
    event_id = data.get('event_id')
    scores = data.get('scores', [])
    
    if not event_id or not scores:
        return jsonify({'success': False, 'message': '参数错误'}), 400
    
    try:
        for score_data in scores:
            score_id = score_data.get('score_id')
            result = score_data.get('result')
            ranking = score_data.get('ranking')
            points = score_data.get('points')
            
            score = Score.query.get(score_id)
            if score:
                if result is not None:
                    score.result = result
                if ranking is not None:
                    score.ranking = ranking
                if points is not None:
                    score.points = points
        
        db.session.commit()
        return jsonify({'success': True, 'message': '成绩批量更新成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500

@event_athlete_bp.route('/import_scores', methods=['POST'])
@login_required
@admin_required
def import_scores():
    """
    导入成绩
    
    Returns:
        JSON响应
    """
    event_id = request.form.get('event_id', type=int)
    file = request.files.get('file')
    
    if not event_id or not file:
        return jsonify({'success': False, 'message': '参数错误'}), 400
    
    try:
        # 读取CSV文件
        stream = io.StringIO(file.stream.read().decode('utf-8-sig'))
        csv_reader = csv.DictReader(stream)
        
        # 获取已经参加该项目的运动员
        existing_athletes = db.session.query(Score.athlete_id).filter(Score.event_id == event_id).all()
        existing_athlete_ids = [a[0] for a in existing_athletes]
        
        # 获取所有运动员的学号映射
        athletes = Athlete.query.all()
        student_id_map = {a.student_id: a.athlete_id for a in athletes}
        
        added_count = 0
        updated_count = 0
        
        for row in csv_reader:
            student_id = row.get('学号')
            result = row.get('成绩')
            ranking = row.get('排名')
            points = row.get('得分')
            
            if not student_id:
                continue
            
            athlete_id = student_id_map.get(student_id)
            if not athlete_id:
                continue
            
            # 检查运动员是否已经参加了该比赛项目
            if athlete_id in existing_athlete_ids:
                # 更新现有成绩
                score = Score.query.filter_by(event_id=event_id, athlete_id=athlete_id).first()
                if score:
                    if result:
                        score.result = result
                    if ranking:
                        score.ranking = int(ranking) if ranking.isdigit() else None
                    if points:
                        score.points = int(points) if points.isdigit() else None
                    updated_count += 1
            else:
                # 创建新成绩记录
                score = Score(
                    event_id=event_id,
                    athlete_id=athlete_id,
                    result=result,
                    ranking=int(ranking) if ranking and ranking.isdigit() else None,
                    points=int(points) if points and points.isdigit() else None
                )
                db.session.add(score)
                added_count += 1
                existing_athlete_ids.append(athlete_id)
        
        db.session.commit()
        return jsonify({
            'success': True, 
            'message': f'成功导入成绩！新增 {added_count} 条记录，更新 {updated_count} 条记录。'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'导入失败: {str(e)}'}), 500

@event_athlete_bp.route('/api/athletes')
@login_required
def api_athletes():
    """
    获取运动员列表API
    
    Returns:
        JSON格式的运动员列表
    """
    # 获取查询参数
    event_id = request.args.get('event_id', type=int)
    department_id = request.args.get('department_id', type=int)
    gender = request.args.get('gender')
    
    if not event_id:
        return jsonify([])
    
    # 获取选中项目
    event = Event.query.get(event_id)
    
    # 构建查询
    query = Athlete.query
    
    # 应用性别限制
    if event and event.gender_limit != '不限':
        query = query.filter(Athlete.gender == event.gender_limit)
    elif gender:
        query = query.filter(Athlete.gender == gender)
    
    # 应用院系筛选
    if department_id:
        query = query.filter(Athlete.dept_id == department_id)
    
    # 获取已经参加该项目的运动员
    existing_athletes = db.session.query(Score.athlete_id).filter(Score.event_id == event_id).all()
    existing_athlete_ids = [a[0] for a in existing_athletes]
    
    # 获取未参加该项目的运动员
    athletes = query.all()
    eligible_athletes = [a for a in athletes if a.athlete_id not in existing_athlete_ids]
    
    # 转换为JSON格式
    result = [{
        'athlete_id': a.athlete_id,
        'name': a.name,
        'gender': a.gender,
        'student_id': a.student_id,
        'dept_id': a.dept_id,
        'dept_name': a.department.dept_name
    } for a in eligible_athletes]
    
    return jsonify(result)
