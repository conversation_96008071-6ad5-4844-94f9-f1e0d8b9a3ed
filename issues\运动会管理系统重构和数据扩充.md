# 运动会管理系统重构和数据扩充任务计划

## 📋 任务概述
- **任务1**：代码重构和优化
- **任务2**：数据库测试数据扩充
- **执行时间**：2025年6月11日
- **预计工作量**：3-4小时

## 🎯 任务1：代码重构和优化

### 1.1 config.py 重构
**文件路径**：`config.py`
**修改范围**：第27-40行
**具体操作**：
- 删除 `ProductionConfig` 类（第31-34行）
- 删除 `config` 字典中的 `'production': ProductionConfig` 配置（第39行）
- 保留开发环境配置，简化配置结构
**预期结果**：配置文件只包含开发环境设置，避免生产环境混淆

### 1.2 modules/database.py 重构
**文件路径**：`modules/database.py`
**修改范围**：第23-94行
**具体操作**：
- 删除 `_database_initialized` 全局变量（第24行）
- 删除 `init_database()` 函数（第26-94行）
- 保留 `init_app()` 函数用于SQLAlchemy初始化
**预期结果**：移除自动数据库初始化逻辑，防止覆盖现有数据

### 1.3 app.py 启动逻辑修改
**文件路径**：`app.py`
**修改范围**：第58-62行
**具体操作**：
- 删除 `with app.app_context():` 代码块
- 删除 `init_database(app)` 调用
- 保留其他应用初始化逻辑
**预期结果**：应用启动时不会自动创建或修改数据库

### 1.4 database.sql 清理
**文件路径**：`database.sql`
**修改范围**：第77-91行
**具体操作**：
- 删除重复的 `users` 表定义
- 保留原有的 `user` 表定义（第3-15行）
- 保留管理员账号插入语句，但修改表名为 `user`
**预期结果**：消除表定义重复，统一使用 `user` 表

## 🎯 任务2：数据库测试数据扩充

### 2.1 院系数据扩充
**目标数量**：10个院系
**数据设计**：
```sql
计算机学院(CS)、电子工程学院(EE)、机械工程学院(ME)、
经济管理学院(EM)、外国语学院(FL)、艺术设计学院(AD)、
数学统计学院(MS)、物理化学学院(PC)、生物医学学院(BM)、体育学院(PE)
```

### 2.2 运动员数据扩充
**目标数量**：50名运动员
**分布策略**：每个院系5名，性别均匀分布
**学号格式**：2023001-2023050
**数据完整性**：确保所有外键关联正确

### 2.3 比赛项目数据扩充
**目标数量**：20个比赛项目
**项目分类**：
- **田赛**：男女跳高、跳远、铅球、标枪（8项）
- **径赛**：男女100m、200m、400m、800m、1500m、4×100m接力（11项）
- **球类**：篮球（1项）
**时间安排**：2025年6月5日-11日，每天3-4个项目

### 2.4 裁判员数据扩充
**目标数量**：15名裁判员
**级别分布**：
- 国家级：3名
- 一级：5名
- 二级：4名
- 三级：3名

### 2.5 项目裁判关联数据
**关联策略**：每个比赛项目分配2-3名裁判员
**角色分配**：主裁判、副裁判、计时员、记录员

### 2.6 成绩数据扩充
**数据要求**：符合大学生真实运动水平
**成绩标准**：
- 男子100m：10.5-12.5秒
- 女子100m：12.0-14.5秒
- 男子跳高：1.60-2.00米
- 女子跳高：1.30-1.70米
- 其他项目按比例设计

## 📝 执行步骤

### 阶段1：代码重构（预计1.5小时）
1. 备份当前代码
2. 修改 config.py
3. 修改 modules/database.py
4. 修改 app.py
5. 清理 database.sql
6. 测试应用启动

### 阶段2：数据扩充（预计2小时）
1. 设计详细的测试数据
2. 编写数据插入SQL语句
3. 创建数据扩充脚本
4. 验证数据完整性
5. 测试数据查询功能

### 阶段3：质量检查（预计0.5小时）
1. 功能测试
2. 数据一致性检查
3. 性能测试
4. 文档更新

## ⚠️ 风险控制
- 在修改前创建代码备份
- 分步执行，每步完成后进行测试
- 保持数据库连接配置的一致性
- 确保外键约束的正确性

## ✅ 验收标准
- 应用启动时不会自动创建/修改数据库
- 配置文件结构清晰，只包含开发环境设置
- 测试数据丰富且真实，满足系统演示需求
- 所有功能模块正常工作
- 数据查询和统计功能正确
