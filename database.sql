-- 创建数据库
CREATE DATABASE IF NOT EXISTS  sports_meetDB;
USE sports_meetDB;


-- 创建用户表
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    role ENUM('admin', 'manager', 'user') NOT NULL DEFAULT 'user',
    status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建院系表
CREATE TABLE department (
    dept_id INT PRIMARY KEY AUTO_INCREMENT,
    dept_name VARCHAR(50) NOT NULL,
    dept_code VARCHAR(10) UNIQUE NOT NULL
);

-- 创建运动员表
CREATE TABLE athlete (
    athlete_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    gender ENUM('男','女') NOT NULL,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    dept_id INT NOT NULL,
    FOREIGN KEY (dept_id) REFERENCES department(dept_id)
);

-- 创建比赛项目表
CREATE TABLE event (
    event_id INT PRIMARY KEY AUTO_INCREMENT,
    event_name VARCHAR(100) NOT NULL,
    event_type ENUM('田赛','径赛','球类','游泳') NOT NULL,
    gender_limit ENUM('男','女','不限') DEFAULT '不限',
    max_participants INT,
    start_time DATETIME,
    location VARCHAR(50)
);

-- 创建裁判员表
CREATE TABLE judge (
    judge_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    level VARCHAR(20),
    contact VARCHAR(20)
);

-- 创建项目裁判关联表
CREATE TABLE event_judge (
    event_id INT,
    judge_id INT,
    role VARCHAR(20),
    PRIMARY KEY (event_id, judge_id),
    FOREIGN KEY (event_id) REFERENCES event(event_id),
    FOREIGN KEY (judge_id) REFERENCES judge(judge_id)
);

-- 创建成绩表
CREATE TABLE score (
    score_id INT PRIMARY KEY AUTO_INCREMENT,
    athlete_id INT NOT NULL,
    event_id INT NOT NULL,
    result VARCHAR(20),
    ranking INT,
    points INT,
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (athlete_id) REFERENCES athlete(athlete_id),
    FOREIGN KEY (event_id) REFERENCES event(event_id),
    UNIQUE KEY (athlete_id, event_id)
);

-- 插入默认管理员账号
INSERT INTO users (username, password, real_name, role) VALUES ('admin', 'admin', '系统管理员', 'admin');


-- 插入测试数据

-- 院系数据（10个院系）
INSERT INTO department (dept_name, dept_code) VALUES
('计算机学院', 'CS'),
('电子工程学院', 'EE'),
('机械工程学院', 'ME'),
('经济管理学院', 'EM'),
('外国语学院', 'FL'),
('艺术设计学院', 'AD'),
('数学统计学院', 'MS'),
('物理化学学院', 'PC'),
('生物医学学院', 'BM'),
('体育学院', 'PE');

-- 运动员数据（50名运动员，每个院系5名，性别均匀分布）
INSERT INTO athlete (name, gender, student_id, dept_id) VALUES
-- 计算机学院
('张伟', '男', '2023001', 1),
('李娜', '女', '2023002', 1),
('王强', '男', '2023003', 1),
('刘敏', '女', '2023004', 1),
('陈浩', '男', '2023005', 1),
-- 电子工程学院
('赵雷', '男', '2023006', 2),
('孙丽', '女', '2023007', 2),
('周杰', '男', '2023008', 2),
('吴静', '女', '2023009', 2),
('郑斌', '男', '2023010', 2),
-- 机械工程学院
('冯涛', '男', '2023011', 3),
('何琳', '女', '2023012', 3),
('蒋鹏', '男', '2023013', 3),
('韩梅', '女', '2023014', 3),
('曹磊', '男', '2023015', 3),
-- 经济管理学院
('邓辉', '男', '2023016', 4),
('彭芳', '女', '2023017', 4),
('许刚', '男', '2023018', 4),
('苏萍', '女', '2023019', 4),
('卢军', '男', '2023020', 4),
-- 外国语学院
('丁亮', '男', '2023021', 5),
('范红', '女', '2023022', 5),
('石勇', '男', '2023023', 5),
('姚洁', '女', '2023024', 5),
('龚伟', '男', '2023025', 5),
-- 艺术设计学院
('谭峰', '男', '2023026', 6),
('黎美', '女', '2023027', 6),
('廖强', '男', '2023028', 6),
('易慧', '女', '2023029', 6),
('常斌', '男', '2023030', 6),
-- 数学统计学院
('乔亮', '男', '2023031', 7),
('史娟', '女', '2023032', 7),
('钱勇', '男', '2023033', 7),
('孔玲', '女', '2023034', 7),
('尹涛', '男', '2023035', 7),
-- 物理化学学院
('姜峰', '男', '2023036', 8),
('戚丽', '女', '2023037', 8),
('谢强', '男', '2023038', 8),
('闫敏', '女', '2023039', 8),
('顾浩', '男', '2023040', 8),
-- 生物医学学院
('毛雷', '男', '2023041', 9),
('温静', '女', '2023042', 9),
('康杰', '男', '2023043', 9),
('滕琳', '女', '2023044', 9),
('汪斌', '男', '2023045', 9),
-- 体育学院
('包涛', '男', '2023046', 10),
('左芳', '女', '2023047', 10),
('鲍鹏', '男', '2023048', 10),
('水梅', '女', '2023049', 10),
('祝磊', '男', '2023050', 10);

-- 比赛项目数据（20个项目，时间安排在2025年6月5日-11日）
INSERT INTO event (event_name, event_type, gender_limit, max_participants, start_time, location) VALUES
-- 6月5日 田赛项目
('男子跳高', '田赛', '男', 12, '2025-06-05 09:00:00', '田径场跳高区'),
('女子跳高', '田赛', '女', 10, '2025-06-05 10:30:00', '田径场跳高区'),
('男子跳远', '田赛', '男', 15, '2025-06-05 14:00:00', '田径场跳远区'),
('女子跳远', '田赛', '女', 12, '2025-06-05 15:30:00', '田径场跳远区'),
-- 6月6日 径赛项目
('男子100米', '径赛', '男', 16, '2025-06-06 09:00:00', '田径场跑道'),
('女子100米', '径赛', '女', 14, '2025-06-06 09:30:00', '田径场跑道'),
('男子200米', '径赛', '男', 14, '2025-06-06 10:30:00', '田径场跑道'),
('女子200米', '径赛', '女', 12, '2025-06-06 11:00:00', '田径场跑道'),
-- 6月7日 中长距离项目
('男子400米', '径赛', '男', 12, '2025-06-07 09:00:00', '田径场跑道'),
('女子400米', '径赛', '女', 10, '2025-06-07 09:30:00', '田径场跑道'),
('男子800米', '径赛', '男', 10, '2025-06-07 10:30:00', '田径场跑道'),
('女子800米', '径赛', '女', 8, '2025-06-07 11:00:00', '田径场跑道'),
-- 6月8日 长距离和投掷项目
('男子1500米', '径赛', '男', 8, '2025-06-08 09:00:00', '田径场跑道'),
('女子1500米', '径赛', '女', 6, '2025-06-08 09:30:00', '田径场跑道'),
('男子铅球', '田赛', '男', 10, '2025-06-08 14:00:00', '田径场投掷区'),
('女子铅球', '田赛', '女', 8, '2025-06-08 15:00:00', '田径场投掷区'),
-- 6月9日 投掷和接力项目
('男子标枪', '田赛', '男', 8, '2025-06-09 09:00:00', '田径场投掷区'),
('女子标枪', '田赛', '女', 6, '2025-06-09 10:00:00', '田径场投掷区'),
('4×100米接力', '径赛', '不限', 20, '2025-06-09 16:00:00', '田径场跑道'),
-- 6月10日 球类项目
('篮球比赛', '球类', '不限', 24, '2025-06-10 09:00:00', '篮球场');

-- 裁判员数据（15名裁判员，不同级别分布）
INSERT INTO judge (name, level, contact) VALUES
-- 国家级裁判（3名）
('刘建国', '国家级', '13800138001'),
('陈志华', '国家级', '13800138002'),
('王晓东', '国家级', '13800138003'),
-- 一级裁判（5名）
('李明', '一级', '13800138004'),
('张红', '一级', '13800138005'),
('赵强', '一级', '13800138006'),
('孙丽华', '一级', '13800138007'),
('周杰伦', '一级', '13800138008'),
-- 二级裁判（4名）
('吴静', '二级', '13800138009'),
('郑斌', '二级', '13800138010'),
('冯涛', '二级', '13800138011'),
('何琳', '二级', '13800138012'),
-- 三级裁判（3名）
('蒋鹏', '三级', '13800138013'),
('韩梅', '三级', '13800138014'),
('曹磊', '三级', '13800138015');

-- 项目裁判关联数据（为每个比赛项目分配2-3名裁判员）
INSERT INTO event_judge (event_id, judge_id, role) VALUES
-- 田赛项目裁判分配
(1, 1, '主裁判'), (1, 4, '副裁判'), (1, 9, '记录员'),
(2, 2, '主裁判'), (2, 5, '副裁判'), (2, 10, '记录员'),
(3, 3, '主裁判'), (3, 6, '副裁判'), (3, 11, '记录员'),
(4, 1, '主裁判'), (4, 7, '副裁判'), (4, 12, '记录员'),
-- 径赛项目裁判分配
(5, 2, '主裁判'), (5, 8, '计时员'), (5, 13, '记录员'),
(6, 3, '主裁判'), (6, 4, '计时员'), (6, 14, '记录员'),
(7, 1, '主裁判'), (7, 5, '计时员'), (7, 15, '记录员'),
(8, 2, '主裁判'), (8, 6, '计时员'), (8, 9, '记录员'),
(9, 3, '主裁判'), (9, 7, '计时员'), (9, 10, '记录员'),
(10, 1, '主裁判'), (10, 8, '计时员'), (10, 11, '记录员'),
(11, 2, '主裁判'), (11, 4, '计时员'), (11, 12, '记录员'),
(12, 3, '主裁判'), (12, 5, '计时员'), (12, 13, '记录员'),
(13, 1, '主裁判'), (13, 6, '计时员'), (13, 14, '记录员'),
(14, 2, '主裁判'), (14, 7, '计时员'), (14, 15, '记录员'),
-- 投掷项目裁判分配
(15, 3, '主裁判'), (15, 8, '副裁判'), (15, 9, '记录员'),
(16, 1, '主裁判'), (16, 4, '副裁判'), (16, 10, '记录员'),
(17, 2, '主裁判'), (17, 5, '副裁判'), (17, 11, '记录员'),
(18, 3, '主裁判'), (18, 6, '副裁判'), (18, 12, '记录员'),
-- 接力和球类项目裁判分配
(19, 1, '主裁判'), (19, 7, '计时员'), (19, 13, '记录员'),
(20, 2, '主裁判'), (20, 8, '副裁判'), (20, 14, '记录员');

-- 成绩数据（符合大学生真实运动水平）
INSERT INTO score (athlete_id, event_id, result, ranking, points) VALUES
-- 男子跳高成绩（event_id=1）
(1, 1, '1.75米', 1, 9),
(3, 1, '1.70米', 2, 7),
(5, 1, '1.68米', 3, 6),
(11, 1, '1.65米', 4, 5),
(13, 1, '1.62米', 5, 4),
(15, 1, '1.60米', 6, 3),
-- 女子跳高成绩（event_id=2）
(2, 2, '1.55米', 1, 9),
(4, 2, '1.50米', 2, 7),
(12, 2, '1.48米', 3, 6),
(14, 2, '1.45米', 4, 5),
(22, 2, '1.42米', 5, 4),
-- 男子跳远成绩（event_id=3）
(6, 3, '6.85米', 1, 9),
(8, 3, '6.72米', 2, 7),
(10, 3, '6.58米', 3, 6),
(16, 3, '6.45米', 4, 5),
(18, 3, '6.32米', 5, 4),
(20, 3, '6.18米', 6, 3),
-- 女子跳远成绩（event_id=4）
(7, 4, '5.45米', 1, 9),
(9, 4, '5.32米', 2, 7),
(17, 4, '5.18米', 3, 6),
(19, 4, '5.05米', 4, 5),
(24, 4, '4.92米', 5, 4),
-- 男子100米成绩（event_id=5）
(21, 5, '10.85秒', 1, 9),
(23, 5, '10.92秒', 2, 7),
(25, 5, '11.08秒', 3, 6),
(31, 5, '11.15秒', 4, 5),
(33, 5, '11.22秒', 5, 4),
(35, 5, '11.35秒', 6, 3),
-- 女子100米成绩（event_id=6）
(22, 6, '12.45秒', 1, 9),
(24, 6, '12.58秒', 2, 7),
(26, 6, '12.72秒', 3, 6),
(32, 6, '12.85秒', 4, 5),
(34, 6, '12.98秒', 5, 4),
-- 男子200米成绩（event_id=7）
(27, 7, '22.15秒', 1, 9),
(29, 7, '22.38秒', 2, 7),
(36, 7, '22.65秒', 3, 6),
(38, 7, '22.82秒', 4, 5),
(40, 7, '23.05秒', 5, 4),
-- 女子200米成绩（event_id=8）
(28, 8, '25.85秒', 1, 9),
(30, 8, '26.12秒', 2, 7),
(37, 8, '26.45秒', 3, 6),
(39, 8, '26.78秒', 4, 5),
-- 男子400米成绩（event_id=9）
(41, 9, '50.25秒', 1, 9),
(43, 9, '50.68秒', 2, 7),
(45, 9, '51.15秒', 3, 6),
(46, 9, '51.52秒', 4, 5),
(48, 9, '51.98秒', 5, 4),
-- 女子400米成绩（event_id=10）
(42, 10, '58.45秒', 1, 9),
(44, 10, '58.92秒', 2, 7),
(47, 10, '59.38秒', 3, 6),
(49, 10, '59.85秒', 4, 5),
-- 男子800米成绩（event_id=11）
(50, 11, '2:02.15', 1, 9),
(1, 11, '2:03.45', 2, 7),
(3, 11, '2:04.82', 3, 6),
(5, 11, '2:06.15', 4, 5),
(11, 11, '2:07.68', 5, 4),
-- 女子800米成绩（event_id=12）
(2, 12, '2:25.35', 1, 9),
(4, 12, '2:26.78', 2, 7),
(12, 12, '2:28.15', 3, 6),
(14, 12, '2:29.52', 4, 5),
-- 男子1500米成绩（event_id=13）
(13, 13, '4:15.25', 1, 9),
(15, 13, '4:18.68', 2, 7),
(21, 13, '4:22.15', 3, 6),
(23, 13, '4:25.82', 4, 5),
-- 女子1500米成绩（event_id=14）
(22, 14, '5:05.45', 1, 9),
(24, 14, '5:08.92', 2, 7),
(26, 14, '5:12.38', 3, 6),
-- 男子铅球成绩（event_id=15）
(25, 15, '12.85米', 1, 9),
(27, 15, '12.42米', 2, 7),
(29, 15, '11.98米', 3, 6),
(31, 15, '11.55米', 4, 5),
(33, 15, '11.12米', 5, 4),
-- 女子铅球成绩（event_id=16）
(28, 16, '9.85米', 1, 9),
(30, 16, '9.42米', 2, 7),
(32, 16, '8.98米', 3, 6),
(34, 16, '8.55米', 4, 5),
-- 男子标枪成绩（event_id=17）
(35, 17, '45.25米', 1, 9),
(36, 17, '43.68米', 2, 7),
(38, 17, '42.15米', 3, 6),
(40, 17, '40.82米', 4, 5),
-- 女子标枪成绩（event_id=18）
(37, 18, '32.45米', 1, 9),
(39, 18, '31.12米', 2, 7),
(41, 18, '29.85米', 3, 6),
-- 4×100米接力成绩（event_id=19）- 以队为单位，使用第一名运动员代表
(42, 19, '42.15秒', 1, 9),  -- 生物医学学院队
(43, 19, '42.68秒', 2, 7),  -- 生物医学学院队
(44, 19, '43.25秒', 3, 6),  -- 生物医学学院队
(46, 19, '43.82秒', 4, 5),  -- 体育学院队
(48, 19, '44.35秒', 5, 4),  -- 体育学院队
-- 篮球比赛成绩（event_id=20）- 以队为单位
(45, 20, '胜', 1, 9),       -- 生物医学学院队
(47, 20, '胜', 2, 7),       -- 体育学院队
(49, 20, '胜', 3, 6),       -- 体育学院队
(50, 20, '负', 4, 5);


