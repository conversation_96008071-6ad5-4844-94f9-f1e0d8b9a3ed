
-- 创建用户表
CREATE TABLE user (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    role ENUM('admin', 'manager', 'user') NOT NULL DEFAULT 'user',
    status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建院系表
CREATE TABLE department (
    dept_id INT PRIMARY KEY AUTO_INCREMENT,
    dept_name VARCHAR(50) NOT NULL,
    dept_code VARCHAR(10) UNIQUE NOT NULL
);

-- 创建运动员表
CREATE TABLE athlete (
    athlete_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    gender ENUM('男','女') NOT NULL,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    dept_id INT NOT NULL,
    FOREIGN KEY (dept_id) REFERENCES department(dept_id)
);

-- 创建比赛项目表
CREATE TABLE event (
    event_id INT PRIMARY KEY AUTO_INCREMENT,
    event_name VARCHAR(100) NOT NULL,
    event_type ENUM('田赛','径赛','球类','游泳') NOT NULL,
    gender_limit ENUM('男','女','不限') DEFAULT '不限',
    max_participants INT,
    start_time DATETIME,
    location VARCHAR(50)
);

-- 创建裁判员表
CREATE TABLE judge (
    judge_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    level VARCHAR(20),
    contact VARCHAR(20)
);

-- 创建项目裁判关联表
CREATE TABLE event_judge (
    event_id INT,
    judge_id INT,
    role VARCHAR(20),
    PRIMARY KEY (event_id, judge_id),
    FOREIGN KEY (event_id) REFERENCES event(event_id),
    FOREIGN KEY (judge_id) REFERENCES judge(judge_id)
);

-- 创建成绩表
CREATE TABLE score (
    score_id INT PRIMARY KEY AUTO_INCREMENT,
    athlete_id INT NOT NULL,
    event_id INT NOT NULL,
    result VARCHAR(20),
    ranking INT,
    points INT,
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (athlete_id) REFERENCES athlete(athlete_id),
    FOREIGN KEY (event_id) REFERENCES event(event_id),
    UNIQUE KEY (athlete_id, event_id)
);

CREATE TABLE users(
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50),
    email VARCHAR(100),
    phone VARCHAR(20),
    role ENUM('admin', 'manager', 'user') NOT NULL DEFAULT 'user',
    status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入默认管理员账号
INSERT INTO users (username, password, real_name, role) VALUES ('admin', 'admin', '系统管理员', 'admin');


-- 插入测试数据
-- 院系数据
INSERT INTO department (dept_name, dept_code) VALUES 
('计算机学院', 'CS'),
('电子工程学院', 'EE'),
('机械工程学院', 'ME');

-- 运动员数据
INSERT INTO athlete (name, gender, student_id, dept_id) VALUES 
('张三', '男', '2023001', 1),
('李四', '男', '2023002', 1),
('王五', '女', '2023003', 2),
('赵六', '女', '2023004', 3);

-- 比赛项目数据
INSERT INTO event (event_name, event_type, gender_limit, max_participants, start_time, location) VALUES 
('男子100米', '径赛', '男', 8, '2023-11-20 09:00:00', '田径场'),
('女子跳高', '田赛', '女', 10, '2023-11-20 10:30:00', '跳高场地'),
('4×100米接力', '径赛', '不限', 12, '2023-11-21 14:00:00', '田径场');

-- 裁判员数据
INSERT INTO judge (name, level, contact) VALUES 
('刘老师', '国家级', '13800138001'),
('陈老师', '一级', '13800138002'),
('杨老师', '二级', '13800138003');

-- 项目裁判关联数据
INSERT INTO event_judge (event_id, judge_id, role) VALUES 
(1, 1, '主裁判'),
(1, 2, '计时员'),
(2, 3, '主裁判'),
(3, 1, '主裁判'),
(3, 2, '计时员');

-- 成绩数据
INSERT INTO score (athlete_id, event_id, result, ranking, points) VALUES 
(1, 1, '11.23秒', 1, 9),
(2, 1, '11.45秒', 2, 7),
(3, 2, '1.65米', 1, 9),
(4, 3, '52.34秒', 2, 7);

